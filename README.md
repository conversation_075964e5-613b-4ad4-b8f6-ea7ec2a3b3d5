# Listless Development Environment

This repository contains the development environment, testing infrastructure, and supporting tools for the Listless task management application.

## 📁 Repository Structure

```
Listless-1/
├── 📱 Listless_V0_8-6/          # Main Listless application (React/Next.js)
├── 🧪 testing/                  # Testing infrastructure and Playwright setup
├── 📚 docs/                     # Project documentation and guides
├── ⚙️  config/                  # Configuration files and templates
├── 🔧 lib/                      # Utility libraries and agent coordination
├── 🗄️  taskmaster_subdirectory_backup_20250613_1136.tar.gz  # Backup archive
├── .gitignore                   # Git ignore rules
└── README.md                    # This file
```

## 🚀 Quick Start

### 1. Main Application Development
The Listless application is located in `Listless_V0_8-6/`:
```bash
cd Listless_V0_8-6
npm install
npm run dev
```

### 2. Testing Setup
Set up the testing environment:
```bash
cd testing
./setup-playwright-tests.sh
./setup-test-database.sh
./verify-test-setup.sh
```

### 3. Environment Configuration
Configure your development environment:
```bash
cp config/.env.example .env
# Edit .env with your API keys
```

## 📂 Directory Details

### 📱 **Listless_V0_8-6/**
- **Purpose**: Main Listless task management application
- **Technology**: React, Next.js, TypeScript, Supabase
- **Repository**: Connected to `https://github.com/josh000111/Listless_V0_8-6.git`
- **Branch**: Currently on `test/remote-agent-simulation`

### 🧪 **testing/**
- **Purpose**: Comprehensive testing infrastructure
- **Contents**: Playwright examples, setup scripts, test documentation
- **Key Files**: Setup scripts, test configurations, user strategies

### 📚 **docs/**
- **Purpose**: Project documentation and guides
- **Contents**: Handoff guides, technical documentation, issue resolutions
- **Audience**: Developers, freelancers, project managers

### ⚙️ **config/**
- **Purpose**: Configuration files and templates
- **Contents**: Environment variable templates, development configurations
- **Usage**: Copy templates and customize for your environment

### 🔧 **lib/**
- **Purpose**: Development utilities and agent coordination
- **Contents**: Agent coordination utilities, helper functions
- **Usage**: Supporting multi-agent development workflows

## 🔄 Development Workflow

1. **Application Development**: Work in `Listless_V0_8-6/` directory
2. **Testing**: Use scripts and examples in `testing/` directory
3. **Documentation**: Reference and update files in `docs/` directory
4. **Configuration**: Manage environment settings via `config/` directory

## 📝 Important Notes

- The `Listless_V0_8-6/` directory is a separate Git repository
- Testing infrastructure is designed to work with the main application
- Configuration files contain templates - copy and customize as needed
- Documentation should be kept up-to-date as the project evolves

## 🤝 Contributing

1. Review the appropriate documentation in `docs/`
2. Set up your environment using `config/` templates
3. Run tests using `testing/` infrastructure
4. Work on the application in `Listless_V0_8-6/`

## 🔗 Related Resources

- **Main Application Repository**: https://github.com/josh000111/Listless_V0_8-6.git
- **Testing Documentation**: `testing/README.md`
- **Configuration Guide**: `config/README.md`
- **Project Documentation**: `docs/README.md`
