# Listless Web Application - Manual Testing Checklist

## Overview
This comprehensive testing checklist covers all implemented features through TaskMaster AI Tasks 1-4, focusing exclusively on user-facing functionality that can be tested through the web UI without requiring technical knowledge or database access.

## Prerequisites
- Application running at `http://localhost:3000`
- Valid email address for testing authentication flows
- <PERSON><PERSON><PERSON> with JavaScript enabled
- Clear browser cache/cookies before starting

---

## 1. Authentication & User Management

### 1.1 User Registration
**Test Case**: New User Signup
- **Prerequisites**: No existing account with test email
- **Steps**:
  1. Navigate to `/auth/signup`
  2. Fill in "Full name" field with valid name
  3. Enter valid email address
  4. Create password meeting requirements (8+ chars, uppercase, lowercase, number, special char)
  5. Confirm password matches
  6. Click "Create account" button
- **Expected Results**:
  - Password requirements show green checkmarks as criteria are met
  - Form validates password match in real-time
  - Success message appears after submission
  - Redirected to email verification page
  - Verification email received in inbox
- **Success Criteria**: Account created, verification email sent

**Test Case**: Email Verification
- **Prerequisites**: Completed signup process
- **Steps**:
  1. Check email inbox for verification message
  2. Click verification link in email
  3. Observe redirect behavior
- **Expected Results**:
  - Redirected to dashboard after verification
  - User session established
  - Welcome message displayed
- **Success Criteria**: Email verified, automatic login successful

### 1.2 User Login
**Test Case**: Valid Login
- **Prerequisites**: Verified user account
- **Steps**:
  1. Navigate to `/auth/login`
  2. Enter registered email address
  3. Enter correct password
  4. Click "Sign in" button
- **Expected Results**:
  - Loading spinner appears during authentication
  - Redirected to dashboard on success
  - User profile visible in sidebar
- **Success Criteria**: Successful authentication and dashboard access

**Test Case**: Invalid Login Attempts
- **Prerequisites**: None
- **Steps**:
  1. Navigate to `/auth/login`
  2. Test with invalid email format
  3. Test with unregistered email
  4. Test with wrong password
  5. Test with empty fields
- **Expected Results**:
  - Appropriate error messages displayed
  - Form validation prevents submission with invalid data
  - No redirect occurs on failed attempts
- **Success Criteria**: Proper error handling and validation feedback

### 1.3 Password Management
**Test Case**: Password Reset Request
- **Prerequisites**: Registered user account
- **Steps**:
  1. Navigate to `/auth/reset-password`
  2. Enter registered email address
  3. Click "Send reset link" button
  4. Check email for reset link
- **Expected Results**:
  - Success message displayed
  - Reset email received
  - Link in email is functional
- **Success Criteria**: Reset email sent and received

**Test Case**: Password Update
- **Prerequisites**: Valid reset link from email
- **Steps**:
  1. Click reset link from email
  2. Redirected to `/auth/update-password`
  3. Enter new password meeting requirements
  4. Confirm new password
  5. Click "Update password" button
- **Expected Results**:
  - Password requirements validation works
  - Success message on update
  - Redirected to dashboard
  - Can login with new password
- **Success Criteria**: Password successfully updated

### 1.4 User Profile & Settings
**Test Case**: Account Settings Access
- **Prerequisites**: Logged in user
- **Steps**:
  1. Click user profile dropdown in sidebar
  2. Select "Settings" option
  3. Navigate to "Account" section
- **Expected Results**:
  - Settings page loads correctly
  - User information displayed accurately
  - Navigation between settings sections works
- **Success Criteria**: Settings accessible and functional

**Test Case**: Email Change
- **Prerequisites**: Logged in user
- **Steps**:
  1. Navigate to Account Settings
  2. Click "Change Email" button
  3. Enter current password
  4. Enter new email address
  5. Submit form
  6. Check both old and new email for confirmation
- **Expected Results**:
  - Password verification required
  - Confirmation emails sent to both addresses
  - Email updated after confirmation
- **Success Criteria**: Email change process completes successfully

**Test Case**: Account Deletion
- **Prerequisites**: Logged in user
- **Steps**:
  1. Navigate to Account Settings
  2. Click "Delete Account" button
  3. Enter current password for verification
  4. Confirm deletion in modal
- **Expected Results**:
  - Password verification required
  - Confirmation dialog appears
  - Account and all data deleted
  - Redirected to home page
  - Cannot login with deleted credentials
- **Success Criteria**: Account completely removed

### 1.5 Session Management
**Test Case**: Logout Functionality
- **Prerequisites**: Logged in user
- **Steps**:
  1. Click user profile dropdown
  2. Select "Sign out" option
  3. Attempt to access dashboard directly
- **Expected Results**:
  - User logged out immediately
  - Redirected to login page
  - Dashboard inaccessible without login
- **Success Criteria**: Session properly terminated

**Test Case**: Session Persistence
- **Prerequisites**: Logged in user
- **Steps**:
  1. Refresh browser page
  2. Close and reopen browser tab
  3. Navigate away and return to site
- **Expected Results**:
  - User remains logged in across refreshes
  - Session persists in new tabs
  - Dashboard accessible without re-login
- **Success Criteria**: Session maintains across browser actions

---

## 2. Task Management

### 2.1 Task Creation
**Test Case**: Basic Task Creation
- **Prerequisites**: Logged in user on dashboard
- **Steps**:
  1. Click "Add Task" button (bottom right)
  2. Enter task content in input field
  3. Press Enter or click save
- **Expected Results**:
  - New task appears in current list
  - Task content matches input
  - Task marked as uncompleted by default
- **Success Criteria**: Task successfully created and visible

**Test Case**: Task Creation with Keyboard Shortcut
- **Prerequisites**: Logged in user on dashboard
- **Steps**:
  1. Press Spacebar while on task list
  2. Enter task content
  3. Press Enter to save
- **Expected Results**:
  - Input field appears immediately
  - Task created on Enter press
  - Focus returns to task list
- **Success Criteria**: Keyboard shortcut works correctly

### 2.2 Task Editing
**Test Case**: Inline Task Editing
- **Prerequisites**: Existing tasks in list
- **Steps**:
  1. Double-click on task content
  2. Modify task text
  3. Press Enter or click outside to save
  4. Press Escape to cancel editing
- **Expected Results**:
  - Task enters edit mode on double-click
  - Changes saved on Enter/blur
  - Changes discarded on Escape
  - Visual feedback during editing
- **Success Criteria**: Inline editing functions properly

**Test Case**: Expanded Task View
- **Prerequisites**: Existing tasks in list
- **Steps**:
  1. Click expand arrow on task
  2. Edit task title in expanded view
  3. Add notes in notes section
  4. Set due date using date picker
  5. Add tags using tag selector
  6. Set priority level
  7. Close expanded view
- **Expected Results**:
  - Expanded view opens smoothly
  - All fields editable and save properly
  - Date picker functional
  - Tag selector allows adding/removing tags
  - Priority selector works
  - Changes persist after closing
- **Success Criteria**: All expanded view features functional

### 2.3 Task Status Management
**Test Case**: Task Completion Toggle
- **Prerequisites**: Existing uncompleted tasks
- **Steps**:
  1. Click checkbox next to task
  2. Observe visual changes
  3. Click checkbox again to uncheck
- **Expected Results**:
  - Task marked as completed with visual indication
  - Task content shows strikethrough or opacity change
  - Checkbox state toggles correctly
  - Status persists across page refreshes
- **Success Criteria**: Task completion status toggles properly

### 2.4 Task Organization
**Test Case**: Task Drag and Drop Reordering
- **Prerequisites**: Multiple tasks in list
- **Steps**:
  1. Click and drag a task to new position
  2. Release to drop in new location
  3. Verify new order persists
- **Expected Results**:
  - Visual feedback during drag operation
  - Task moves to new position on drop
  - Order maintained after page refresh
- **Success Criteria**: Drag and drop reordering works

**Test Case**: Multi-Task Selection
- **Prerequisites**: Multiple tasks in list
- **Steps**:
  1. Hold Cmd/Ctrl and click multiple tasks
  2. Observe selection indicators
  3. Right-click for context menu
  4. Perform bulk operations
- **Expected Results**:
  - Multiple tasks can be selected
  - Visual indication of selection
  - Context menu shows bulk options
  - Bulk operations affect all selected tasks
- **Success Criteria**: Multi-selection functionality works

### 2.5 Task Context Menu Actions
**Test Case**: Right-Click Context Menu
- **Prerequisites**: Existing tasks in list
- **Steps**:
  1. Right-click on task
  2. Test each menu option:
     - Set Due Date
     - Add Tags
     - Set Priority
     - Duplicate Task
     - Convert to Project
     - Move to Trash
     - Share Task
- **Expected Results**:
  - Context menu appears on right-click
  - All menu options functional
  - Appropriate dialogs/pickers open
  - Actions complete successfully
- **Success Criteria**: All context menu actions work

---

## 3. Project & Area Management

### 3.1 Project Creation
**Test Case**: New Project Creation
- **Prerequisites**: Logged in user on dashboard
- **Steps**:
  1. Click "+" button next to "Projects" in sidebar
  2. Select "New Project" from dropdown
  3. Enter project name
  4. Press Enter to save
- **Expected Results**:
  - New project appears in sidebar
  - Project name editable inline
  - Project accessible by clicking
- **Success Criteria**: Project created and accessible

**Test Case**: Task to Project Conversion
- **Prerequisites**: Existing task in list
- **Steps**:
  1. Right-click on task
  2. Select "Convert to Project"
  3. Modify project name if desired
  4. Select area (optional)
  5. Click "Convert to Project"
- **Expected Results**:
  - Conversion dialog appears
  - Project name pre-filled with task content
  - Area selection available
  - Original task removed, project created
  - Task notes transferred to project description
- **Success Criteria**: Task successfully converted to project

### 3.2 Area Management
**Test Case**: Area Creation
- **Prerequisites**: Logged in user on dashboard
- **Steps**:
  1. Click "+" button next to "Projects" in sidebar
  2. Select "New Area" from dropdown
  3. Enter area name
  4. Press Enter to save
- **Expected Results**:
  - New area appears in sidebar
  - Area name editable inline
  - Area can contain projects
- **Success Criteria**: Area created successfully

**Test Case**: Area Expansion/Collapse
- **Prerequisites**: Area with projects
- **Steps**:
  1. Click chevron icon next to area name
  2. Observe expansion/collapse animation
  3. Verify projects show/hide correctly
- **Expected Results**:
  - Smooth animation during expand/collapse
  - Projects visible when expanded
  - Projects hidden when collapsed
  - State persists across sessions
- **Success Criteria**: Area expansion/collapse works

### 3.3 Project Organization
**Test Case**: Project Assignment to Area
- **Prerequisites**: Existing project and area
- **Steps**:
  1. Drag project from standalone section
  2. Drop onto area in sidebar
  3. Verify project moves to area
- **Expected Results**:
  - Visual feedback during drag
  - Project moves to target area
  - Project accessible within area
  - Change persists across sessions
- **Success Criteria**: Project successfully moved to area

### 3.4 Project View
**Test Case**: Project Detail View
- **Prerequisites**: Existing project with tasks
- **Steps**:
  1. Click on project name in sidebar
  2. Edit project title inline
  3. Add/edit project notes
  4. Create tasks within project
  5. Verify task management within project
- **Expected Results**:
  - Project view loads correctly
  - Title editable inline
  - Notes section functional
  - Task creation/management works
  - Project-specific task list displayed
- **Success Criteria**: Project view fully functional

---

## 4. Navigation & Views

### 4.1 Sidebar Navigation
**Test Case**: Main Navigation Items
- **Prerequisites**: Logged in user
- **Steps**:
  1. Click "Inbox" in sidebar
  2. Click "Today" in sidebar
  3. Click "Scheduled" in sidebar
  4. Click "Logbook" in sidebar
  5. Click "Trash" in sidebar
- **Expected Results**:
  - Each view loads correctly
  - Content appropriate to view type
  - Active view highlighted in sidebar
  - Smooth transitions between views
- **Success Criteria**: All main navigation functional

### 4.2 Specialized Views
**Test Case**: Trash View
- **Prerequisites**: Deleted tasks available
- **Steps**:
  1. Navigate to Trash view
  2. Right-click on trashed task
  3. Select "Restore" option
  4. Verify task returns to original location
  5. Test "Empty Trash" functionality
- **Expected Results**:
  - Trashed tasks visible in Trash view
  - Restore functionality works
  - Tasks return to original lists
  - Empty Trash removes all items
- **Success Criteria**: Trash management functional

**Test Case**: Completed View
- **Prerequisites**: Completed tasks available
- **Steps**:
  1. Navigate to Completed view
  2. Verify completed tasks displayed
  3. Test date filtering (all, today, week, month)
  4. Test sorting options (newest/oldest)
  5. Verify task interaction in completed view
- **Expected Results**:
  - Completed tasks shown with proper filtering
  - Sorting functions correctly
  - Tasks organized by completion date
  - Consistent layout with other views
- **Success Criteria**: Completed view displays and functions correctly

### 4.3 Search Functionality
**Test Case**: Global Search
- **Prerequisites**: Various tasks and projects
- **Steps**:
  1. Click search icon in sidebar
  2. Enter search terms
  3. Review search results
  4. Click on result to navigate
- **Expected Results**:
  - Search dialog opens
  - Results update as typing
  - Results include tasks and projects
  - Clicking result navigates correctly
- **Success Criteria**: Search functionality works

---

## 5. Data Persistence & Error Handling

### 5.1 Data Persistence
**Test Case**: Cross-Session Data Persistence
- **Prerequisites**: Created tasks, projects, areas
- **Steps**:
  1. Create various content
  2. Log out and log back in
  3. Refresh browser
  4. Close and reopen browser
- **Expected Results**:
  - All data persists across sessions
  - No data loss on refresh
  - Consistent state maintained
- **Success Criteria**: Data reliably persists

### 5.2 Error Handling
**Test Case**: Network Error Handling
- **Prerequisites**: Active session
- **Steps**:
  1. Disconnect internet
  2. Attempt various operations
  3. Reconnect internet
  4. Verify error messages and recovery
- **Expected Results**:
  - Appropriate error messages shown
  - Operations queue or fail gracefully
  - Recovery on reconnection
- **Success Criteria**: Graceful error handling

### 5.3 Form Validation
**Test Case**: Input Validation
- **Prerequisites**: Various forms available
- **Steps**:
  1. Test empty required fields
  2. Test invalid email formats
  3. Test password requirements
  4. Test character limits
- **Expected Results**:
  - Validation messages appear
  - Forms prevent invalid submission
  - Real-time feedback provided
- **Success Criteria**: Comprehensive input validation

---

## 6. User Interface & Accessibility

### 6.1 Responsive Design
**Test Case**: Mobile Responsiveness
- **Prerequisites**: Various screen sizes
- **Steps**:
  1. Test on mobile device/emulation
  2. Verify sidebar behavior
  3. Test touch interactions
  4. Check form usability
- **Expected Results**:
  - Layout adapts to screen size
  - Touch targets appropriately sized
  - Navigation remains functional
- **Success Criteria**: Mobile experience functional

### 6.2 Keyboard Navigation
**Test Case**: Keyboard Accessibility
- **Prerequisites**: Keyboard-only navigation
- **Steps**:
  1. Navigate using Tab key
  2. Test Enter/Space for activation
  3. Test Escape for cancellation
  4. Verify focus indicators
- **Expected Results**:
  - All interactive elements reachable
  - Clear focus indicators
  - Logical tab order
  - Keyboard shortcuts work
- **Success Criteria**: Full keyboard accessibility

---

## Testing Notes

### Browser Compatibility
- Test in Chrome, Firefox, Safari, Edge
- Verify consistent behavior across browsers
- Check for browser-specific issues

### Performance Considerations
- Monitor page load times
- Check for smooth animations
- Verify responsive interactions

### Data Integrity
- Verify no data corruption
- Check for proper error recovery
- Confirm backup/restore functionality

### Security Testing
- Verify proper authentication
- Test unauthorized access attempts
- Check for data leakage between users

---

## Completion Criteria

✅ **Authentication**: All auth flows work correctly
✅ **Task Management**: CRUD operations functional
✅ **Project/Area Management**: Organization features work
✅ **Navigation**: All views accessible and functional
✅ **Data Persistence**: Information reliably saved
✅ **Error Handling**: Graceful failure and recovery
✅ **UI/UX**: Responsive and accessible interface

## Issues Found
*Document any issues discovered during testing*

## Test Environment
- **Date**: ___________
- **Tester**: ___________
- **Browser**: ___________
- **Version**: ___________
- **OS**: ___________
