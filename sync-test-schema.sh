#!/bin/bash

# Automated Test Database Schema Sync Script
# Keeps your test Supabase project in sync with production schema changes

set -e  # Exit on any error

echo "🔄 Syncing production schema to test database..."

# Load environment variables
if [ -f "Listless_V0_8-6/.env.local" ]; then
    source Listless_V0_8-6/.env.local
    PROD_URL="$NEXT_PUBLIC_SUPABASE_URL"
    PROD_SERVICE_KEY="$SUPABASE_SERVICE_ROLE_KEY"
fi

if [ -f "Listless_V0_8-6/.env.test" ]; then
    source Listless_V0_8-6/.env.test
    TEST_URL="$NEXT_PUBLIC_SUPABASE_URL"
    TEST_SERVICE_KEY="$SUPABASE_SERVICE_ROLE_KEY"
fi

# Validate configuration
if [ -z "$PROD_URL" ] || [ -z "$TEST_URL" ]; then
    echo "❌ Error: Could not load database URLs from environment files"
    echo "   Please ensure .env.local and .env.test exist with proper credentials"
    exit 1
fi

if [[ "$PROD_URL" == "$TEST_URL" ]]; then
    echo "❌ Error: Production and test URLs are the same!"
    echo "   This would sync production to itself. Please check your .env files."
    exit 1
fi

echo "📊 Configuration:"
echo "   Production: $PROD_URL"
echo "   Test:       $TEST_URL"
echo ""

# Function to execute SQL via Supabase REST API
execute_sql() {
    local url="$1"
    local service_key="$2"
    local sql="$3"
    local description="$4"
    
    echo "🔧 $description..."
    
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        "$url/rest/v1/rpc/exec_sql" \
        -H "Authorization: Bearer $service_key" \
        -H "apikey: $service_key" \
        -H "Content-Type: application/json" \
        -d "{\"sql\": $(echo "$sql" | jq -Rs .)}" 2>/dev/null)
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        echo "   ✅ Success"
        return 0
    else
        echo "   ⚠️ Warning (HTTP $http_code): $response_body"
        return 1
    fi
}

# Function to get schema differences using Supabase CLI
sync_with_cli() {
    echo "🔍 Using Supabase CLI for schema sync..."
    
    # Check if supabase CLI is available
    if ! command -v supabase &> /dev/null; then
        echo "❌ Supabase CLI not found. Please install it first:"
        echo "   brew install supabase/tap/supabase"
        return 1
    fi
    
    # Create temporary directory for sync
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Initialize temporary supabase project
    supabase init --with-vscode-settings=false
    
    # Extract project refs from URLs
    PROD_REF=$(echo "$PROD_URL" | sed 's/https:\/\/\([^.]*\).*/\1/')
    TEST_REF=$(echo "$TEST_URL" | sed 's/https:\/\/\([^.]*\).*/\1/')
    
    echo "📥 Generating schema diff from production ($PROD_REF)..."
    
    # Link to production and generate diff
    supabase link --project-ref "$PROD_REF"
    supabase db diff --use-migra -f sync_$(date +%Y%m%d_%H%M%S)
    
    # Apply to test database
    echo "📤 Applying changes to test database ($TEST_REF)..."
    
    # Get the latest migration file
    LATEST_MIGRATION=$(ls supabase/migrations/*.sql | tail -n1)
    
    if [ -f "$LATEST_MIGRATION" ]; then
        echo "📄 Found migration: $LATEST_MIGRATION"
        
        # Apply migration to test database
        TEST_DB_URL="*********************************************************************/postgres"
        
        if [ -n "$TEST_DB_PASSWORD" ]; then
            PGPASSWORD="$TEST_DB_PASSWORD" psql "$TEST_DB_URL" -f "$LATEST_MIGRATION"
            echo "✅ Migration applied successfully"
        else
            echo "⚠️ TEST_DB_PASSWORD not set, using REST API fallback..."
            sql_content=$(cat "$LATEST_MIGRATION")
            execute_sql "$TEST_URL" "$TEST_SERVICE_KEY" "$sql_content" "Applying schema changes"
        fi
    else
        echo "ℹ️ No schema differences found - databases are in sync"
    fi
    
    # Cleanup
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
}

# Function to sync using REST API (fallback method)
sync_with_api() {
    echo "🔍 Using REST API for basic schema sync..."
    
    # This is a simplified sync that handles common cases
    # For complex schema changes, CLI method is preferred
    
    echo "⚠️ REST API sync is limited. For complex changes, use CLI method."
    echo "   Consider running: supabase db diff and applying manually"
}

# Main sync logic
echo "🚀 Starting schema synchronization..."

# Try CLI method first (most comprehensive)
if sync_with_cli; then
    echo "✅ Schema sync completed using Supabase CLI"
else
    echo "⚠️ CLI sync failed, falling back to manual process"
    echo ""
    echo "📋 Manual sync steps:"
    echo "1. Go to your production Supabase project"
    echo "2. Use SQL Editor to export recent schema changes"
    echo "3. Apply the same changes to your test project"
    echo "4. Or use: supabase db diff --use-migra -f manual_sync"
fi

# Verify test database connectivity
echo "🔌 Verifying test database connectivity..."
if execute_sql "$TEST_URL" "$TEST_SERVICE_KEY" "SELECT 1 as test" "Testing connection"; then
    echo "✅ Test database is accessible"
else
    echo "❌ Cannot connect to test database"
    exit 1
fi

# Optional: Run a quick schema validation
echo "🔍 Validating schema sync..."
validation_sql="
SELECT 
    schemaname,
    tablename,
    COUNT(*) as table_count
FROM pg_tables 
WHERE schemaname = 'public'
GROUP BY schemaname, tablename
ORDER BY tablename;
"

if execute_sql "$TEST_URL" "$TEST_SERVICE_KEY" "$validation_sql" "Checking table structure"; then
    echo "✅ Schema validation completed"
fi

echo ""
echo "🎉 Schema synchronization completed!"
echo ""
echo "📋 Next steps:"
echo "   1. Verify your test environment: ./verify-test-setup.sh"
echo "   2. Run your tests: npm run test:ui"
echo ""
echo "🛡️ Your production database remains completely safe!"
echo "   Only the test database schema was updated."
