# 🛡️ Complete Test Environment Setup Guide for Listless

## 🚨 CRITICAL: Production Database Protection

**Your current setup uses PRODUCTION database credentials. Following this guide will create a completely isolated test environment to protect your production data.**

---

## 📋 Step-by-Step Setup Instructions

### **Step 1: Create Separate Supabase Test Project**

#### 1.1 Create New Project
1. **Go to**: https://supabase.com/dashboard
2. **Click**: "New Project"
3. **Configure**:
   - **Name**: `listless-testing`
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Same as production (recommended)
   - **Organization**: Same as your production project
4. **Click**: "Create new project"
5. **Wait**: 2-3 minutes for initialization

#### 1.2 Get Test Project Credentials
1. **Go to**: Settings > API in your new test project
2. **Copy these values**:
   - **Project URL**: `https://[your-test-ref].supabase.co`
   - **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### **Step 2: Configure Test Environment**

#### 2.1 Update .env.test File
```bash
cd "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6"
```

Edit `.env.test` and replace the placeholder values:
```env
# Replace these with your actual test project values:
NEXT_PUBLIC_SUPABASE_URL=https://YOUR_ACTUAL_TEST_REF.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_test_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_actual_test_service_role_key
```

### **Step 3: Set Up Test Database Schema**

#### 3.1 Method A: Using Setup Script (Recommended)
```bash
# Update the setup script with your test project details
nano setup-test-database.sh

# Update these lines:
TEST_PROJECT_URL="https://YOUR_ACTUAL_TEST_REF.supabase.co"
TEST_SERVICE_KEY="your_actual_test_service_role_key"

# Run the setup script
./setup-test-database.sh
```

#### 3.2 Method B: Manual Setup via Supabase Dashboard
1. **Go to**: Your test project dashboard
2. **Navigate to**: SQL Editor
3. **Execute each migration file** in order:
   - Copy content from `supabase/migrations/001_initial_schema.sql`
   - Paste and run in SQL Editor
   - Repeat for files 002, 003, 004, 005

### **Step 4: Verify Test Environment**

#### 4.1 Run Verification Script
```bash
./verify-test-setup.sh
```

**Expected Output**:
```
✅ Found .env.test file
✅ Required environment variables are set
✅ Safety check passed - not using production database
✅ Test environment flag verified
✅ All required test files are present
✅ Playwright is installed
✅ Test database connection successful
🎉 Test environment verification completed successfully!
```

#### 4.2 Manual Verification
```bash
# Check environment variables
npm run test:verify-env

# Should show your TEST project URL, not production
```

### **Step 5: Run Comprehensive Tests**

#### 5.1 Start Application in Test Mode
```bash
# Terminal 1: Start the application
npm run dev
```

#### 5.2 Execute Test Suite
```bash
# Terminal 2: Run tests with UI (recommended)
npm run test:ui

# Or run specific test suites:
npm run test:auth          # Authentication tests
npm run test:tasks         # Task management tests  
npm run test:projects      # Project/area management tests

# Or run all tests headless:
npm run test
```

---

## 🎯 Test Coverage Summary

### **TaskMaster AI Tasks 1-4 Validation**:

✅ **Task 1: Database Setup**
- Validated through data persistence across test scenarios
- Vector embeddings and AI features tested

✅ **Task 2: Authentication System** (6 test scenarios)
- User registration and email verification
- Login/logout flows with session management
- Password reset functionality
- Account settings and profile management
- Account deletion with complete cleanup
- Session persistence across browser restarts

✅ **Task 3: Task Management API** (8 test scenarios)
- Task creation via button and keyboard shortcuts
- Inline editing and expanded task views
- Task completion status management
- Drag-and-drop reordering functionality
- Context menu actions and bulk operations
- Multi-task selection capabilities
- Search and filtering functionality
- Data persistence validation

✅ **Task 4: Project/Area Management** (7 test scenarios)
- Project and area creation workflows
- Task-to-project conversion functionality
- Hierarchical organization management
- Cascading deletion operations
- Detail view and editing capabilities
- Cross-project task management
- Archive and restore functionality

---

## 🔒 Safety Features

### **Production Protection**:
- ✅ **Environment Isolation**: Separate test Supabase project
- ✅ **Safety Checks**: Automatic verification prevents production access
- ✅ **Test Data Cleanup**: Comprehensive cleanup after each test run
- ✅ **User Management**: Enhanced test user naming and retention policies

### **Test Environment Features**:
- ✅ **Smart User Management**: Descriptive naming with TaskMaster identifiers
- ✅ **Debug Retention**: 24-hour retention for failed test investigation
- ✅ **Batch Operations**: Efficient cleanup of test data
- ✅ **Verification Reports**: Detailed cleanup verification

---

## 🚀 Execution Commands

### **Quick Test Run**:
```bash
# Verify setup
./verify-test-setup.sh

# Start app and run tests
npm run dev &
sleep 10
npm run test:ui
```

### **Comprehensive Testing**:
```bash
# Run all TaskMaster AI validation tests
npm run test

# Generate detailed report
npm run test:report
```

---

## 🛠️ Troubleshooting

### **Common Issues**:

1. **"Tests using production database"**
   - Update `.env.test` with test project credentials
   - Run `./verify-test-setup.sh` to confirm

2. **"Cannot connect to test database"**
   - Verify test project is running in Supabase dashboard
   - Check API keys are correct
   - Ensure network connectivity

3. **"Test files not found"**
   - Run: `cp ../playwright-examples/*.ts tests/`
   - Verify all files copied successfully

4. **"Application won't start"**
   - Check `.env.test` has all required variables
   - Ensure test database schema is applied
   - Verify port 3000 is available

---

## ✅ Success Criteria

**Setup is complete when**:
- ✅ Test environment verification passes
- ✅ Application starts with test database
- ✅ All test suites execute successfully
- ✅ Test reports generate properly
- ✅ Production database remains untouched

**Your production Listless application data will be completely safe throughout the entire testing process.**
