# Playwright Test Setup Guide for Listless Application

## Quick Setup for Another Augment Agent

### Prerequisites
- Listless application at `/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6`
- Node.js and npm installed
- Playwright MCP server available

### 1. Navigate to Application Directory
```bash
cd "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6"
```

### 2. Install Playwright Dependencies
```bash
# Install Playwright testing framework
npm install -D @playwright/test

# Install browser binaries
npx playwright install

# Install additional dependencies if needed
npm install -D @types/node
```

### 3. Copy Test Files to Application
```bash
# Create tests directory
mkdir -p tests

# Copy all test files from examples directory
cp ../playwright-examples/*.ts ./tests/
cp ../playwright-examples/*.md ./tests/

# Verify files copied
ls -la tests/
```

### 4. Update Package.json Scripts
Add these scripts to `package.json`:
```json
{
  "scripts": {
    "test": "playwright test",
    "test:ui": "playwright test --ui",
    "test:headed": "playwright test --headed",
    "test:debug": "playwright test --debug",
    "test:report": "playwright show-report"
  }
}
```

### 5. Start Application
```bash
# Start the development server
npm run dev

# Verify application is running at http://localhost:3000
# You should see the Listless login page
```

### 6. Run Tests

#### Option A: Using npm scripts
```bash
# Run all tests
npm test

# Run with UI mode (recommended for debugging)
npm run test:ui

# Run specific test file
npx playwright test tests/auth.test.ts
```

#### Option B: Using Playwright MCP Server
```bash
# The Playwright MCP server can execute tests directly
# Use browser automation tools available in your environment
```

### 7. View Test Results
```bash
# Generate and view HTML report
npm run test:report

# Results will be in:
# - playwright-report/ (HTML report)
# - test-results/ (screenshots, videos, traces)
```

## Test File Locations

After setup, your test structure should be:
```
Listless_V0_8-6/
├── tests/
│   ├── auth.test.ts                    # Authentication testing
│   ├── task-management.test.ts         # Task CRUD operations
│   ├── project-area-management.test.ts # Project/area management
│   ├── test-utils.ts                   # Helper functions
│   ├── playwright.config.ts            # Configuration
│   ├── test-execution-workflow.md      # Execution guide
│   └── development-integration.md      # CI/CD integration
├── package.json                        # Updated with test scripts
└── ... (rest of application files)
```

## Test Coverage Summary

### TaskMaster AI Tasks 1-4 Coverage:
- ✅ **Task 1 (Database Setup)**: Validated through data persistence tests
- ✅ **Task 2 (Authentication)**: Complete auth flow testing (6 test cases)
- ✅ **Task 3 (Task Management API)**: Full CRUD operations (8 test cases)
- ✅ **Task 4 (Project/Area Management)**: Complete management testing (7 test cases)

### Specific Test Categories:
1. **Authentication & User Management** (6 tests)
   - User registration and email verification
   - Login/logout flows
   - Password reset and management
   - Session persistence
   - Account settings and deletion

2. **Task Management** (8 tests)
   - Task creation (button and keyboard shortcuts)
   - Inline editing and expanded views
   - Completion status management
   - Drag-and-drop reordering
   - Context menu actions
   - Multi-task selection
   - Search functionality

3. **Project & Area Management** (7 tests)
   - Project and area creation
   - Task-to-project conversion
   - Organization and hierarchy
   - Cascading deletion operations
   - Detail view management

4. **Cross-cutting Concerns**
   - Data persistence across sessions
   - Error handling and validation
   - UI responsiveness and accessibility
   - Navigation between views

## Troubleshooting

### Common Issues:

1. **Application Not Running**
   ```bash
   # Ensure application is started
   npm run dev
   # Check http://localhost:3000 is accessible
   ```

2. **Browser Installation Issues**
   ```bash
   # Reinstall browsers
   npx playwright install --force
   ```

3. **Test Failures Due to Timing**
   ```bash
   # Run with slower execution
   npx playwright test --timeout=60000
   ```

4. **Permission Issues**
   ```bash
   # Ensure proper file permissions
   chmod +x tests/*.ts
   ```

### Environment Variables
Create `.env.local` if needed:
```env
# Test environment variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## Execution Commands for Another Agent

### Quick Test Execution:
```bash
# Navigate to project
cd "/Users/<USER>/_DEV PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6"

# Setup (one-time)
npm install -D @playwright/test && npx playwright install
mkdir -p tests && cp ../playwright-examples/*.ts ./tests/

# Start app and run tests
npm run dev &
sleep 10  # Wait for app to start
npx playwright test tests/auth.test.ts --headed

# View results
npx playwright show-report
```

### Using Playwright MCP:
The Playwright MCP server can execute these tests directly using browser automation commands. All test files are properly structured for MCP execution.

## Success Criteria

Tests are successfully set up when:
- ✅ All test files are in `tests/` directory
- ✅ Playwright dependencies installed
- ✅ Application running on localhost:3000
- ✅ Tests execute without setup errors
- ✅ Test reports generate properly

The automated tests comprehensively cover the same scope as the manual testing checklist for TaskMaster Tasks 1-4, providing complete validation of implemented features.
