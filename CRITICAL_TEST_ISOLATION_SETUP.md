# CRITICAL: Test Environment Isolation Setup for Listless

## 🚨 IMMEDIATE ACTION REQUIRED

**Your current Playwright tests are using your PRODUCTION Supabase database!**

This means:
- ❌ Test users are created in your real Auth users table
- ❌ Test data pollutes your actual application database
- ❌ No isolation between test and production data

## 🛡️ SOLUTION: Separate Test Environment

### **Option 1: Separate Supabase Project (Recommended)**

#### Step 1: Create Test Supabase Project
```bash
# Go to https://supabase.com/dashboard
# Click "New Project"
# Name: "listless-testing"
# Use same organization as your main project
```

#### Step 2: Set Up Test Database Schema
```bash
# Copy your production database schema to test project
# In your test project dashboard:
# 1. Go to SQL Editor
# 2. Copy all migration files from supabase/migrations/
# 3. Execute them in order in your test project
```

#### Step 3: Create Test Environment Configuration
```bash
# Create .env.test file
cat > .env.test << 'EOF'
# TEST ENVIRONMENT - ISOLATED FROM PRODUCTION
NEXT_PUBLIC_SUPABASE_URL=https://your-test-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_test_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_test_service_role_key
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Test-specific settings
NODE_ENV=test
PLAYWRIGHT_HEADLESS=false
EOF
```

### **Option 2: Local Supabase Instance (Alternative)**

#### Step 1: Install Supabase CLI
```bash
npm install -g supabase
supabase login
```

#### Step 2: Initialize Local Supabase
```bash
cd /Users/<USER>/_DEV\ PROJECTS/_VSCode/Listless/Listless-1/Listless_V0_8-6
supabase init
supabase start
```

#### Step 3: Configure Test Environment
```bash
# Create .env.test file for local Supabase
cat > .env.test << 'EOF'
# LOCAL TEST ENVIRONMENT
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_SITE_URL=http://localhost:3000

NODE_ENV=test
EOF
```

## 🔧 UPDATED PLAYWRIGHT CONFIGURATION

### Enhanced Test Setup with Environment Isolation
```typescript
// playwright-test.config.ts
import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

export default defineConfig({
  testDir: './tests',
  
  // Ensure we're using test environment
  use: {
    baseURL: 'http://localhost:3000',
    
    // Pass test environment to browser context
    extraHTTPHeaders: {
      'X-Test-Environment': 'true'
    }
  },

  // Global setup to verify test environment
  globalSetup: require.resolve('./tests/test-environment-setup'),
  globalTeardown: require.resolve('./tests/test-environment-teardown'),

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
    env: {
      // Force test environment
      ...process.env,
      NODE_ENV: 'test'
    }
  }
});
```

### Test Environment Verification
```typescript
// tests/test-environment-setup.ts
import { test as setup } from '@playwright/test';

setup('verify test environment', async () => {
  console.log('🔍 Verifying test environment isolation...');
  
  // Check that we're using test Supabase instance
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  
  if (!supabaseUrl?.includes('test') && !supabaseUrl?.includes('localhost')) {
    throw new Error(`
🚨 CRITICAL: Tests are configured to use production database!
Current URL: ${supabaseUrl}
Expected: Test project URL or localhost

Please update .env.test with proper test environment credentials.
    `);
  }
  
  console.log(`✅ Test environment verified: ${supabaseUrl}`);
  
  // Verify test database is empty/clean
  await verifyCleanTestDatabase();
});

async function verifyCleanTestDatabase() {
  // Add verification that test database is in clean state
  console.log('✅ Test database verified as clean');
}
```

## 🧹 ENHANCED CLEANUP STRATEGY

### Guaranteed Test Data Cleanup
```typescript
// tests/test-environment-teardown.ts
import { test as teardown } from '@playwright/test';

teardown('cleanup test environment', async () => {
  console.log('🧹 Starting comprehensive test cleanup...');
  
  try {
    // Clean up all test users
    await cleanupAllTestUsers();
    
    // Clean up all test data
    await cleanupAllTestData();
    
    // Verify cleanup completed
    await verifyCleanupCompleted();
    
    console.log('✅ Test environment cleanup completed');
  } catch (error) {
    console.error('❌ Test cleanup failed:', error);
    // Don't fail the test run, but log the issue
  }
});

async function cleanupAllTestUsers() {
  // Enhanced cleanup with retry logic
  const maxRetries = 3;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await BatchUserManager.emergencyCleanup();
      console.log(`✅ User cleanup successful on attempt ${attempt}`);
      return;
    } catch (error) {
      console.warn(`⚠️ User cleanup attempt ${attempt} failed:`, error);
      if (attempt === maxRetries) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1s before retry
    }
  }
}

async function cleanupAllTestData() {
  // Clean up application data tables
  const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/cleanup_test_data`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    console.warn('⚠️ Test data cleanup failed:', response.statusText);
  }
}
```

## 📊 CLEANUP VERIFICATION TOOLS

### Manual Verification Commands
```bash
# Check for test users in your database
supabase sql --db-url="your_test_database_url" \
  --query="SELECT email FROM auth.users WHERE email LIKE '%test%' OR email LIKE '%playwright%';"

# Check for test data in application tables
supabase sql --db-url="your_test_database_url" \
  --query="SELECT COUNT(*) as test_tasks FROM tasks WHERE title LIKE '%test%';"
```

### Automated Verification Script
```typescript
// scripts/verify-test-cleanup.ts
export async function verifyTestCleanup() {
  const testUsers = await getTestUsers();
  const testTasks = await getTestTasks();
  const testProjects = await getTestProjects();
  
  const report = {
    testUsers: testUsers.length,
    testTasks: testTasks.length,
    testProjects: testProjects.length,
    isClean: testUsers.length === 0 && testTasks.length === 0 && testProjects.length === 0
  };
  
  console.log('🔍 Test cleanup verification:', report);
  
  if (!report.isClean) {
    console.warn('⚠️ Test data found in database - cleanup may have failed');
    console.log('Test users:', testUsers.map(u => u.email));
  }
  
  return report;
}
```

## 🚀 IMMEDIATE ACTION PLAN

### Step 1: Stop Current Test Execution
```bash
# If tests are running, stop them immediately
# Check your Supabase dashboard for test users
```

### Step 2: Clean Up Existing Test Data
```bash
# Go to your Supabase dashboard
# Auth > Users
# Delete any users with emails containing "test" or "playwright"
# 
# SQL Editor - run cleanup query:
# DELETE FROM tasks WHERE user_id IN (
#   SELECT id FROM auth.users WHERE email LIKE '%test%' OR email LIKE '%playwright%'
# );
```

### Step 3: Set Up Test Environment
```bash
# Choose Option 1 (separate project) or Option 2 (local Supabase)
# Create .env.test file with test environment credentials
# Update Playwright config to use test environment
```

### Step 4: Verify Isolation
```bash
# Run test environment verification
npm run test:verify-environment

# Run a single test to confirm isolation
npm run test:auth -- --grep="User Registration"
```

## ⚠️ PRODUCTION SAFETY CHECKLIST

Before running any tests:
- [ ] ✅ Test environment (.env.test) configured
- [ ] ✅ Playwright config uses test environment
- [ ] ✅ Test database URL verified (not production)
- [ ] ✅ Test cleanup mechanisms in place
- [ ] ✅ Production database backup created (safety)

## 🎯 RESULT: COMPLETE ISOLATION

After implementing this setup:
- ✅ **Test User Persistence**: Only in test database, never production
- ✅ **Database Impact**: Zero impact on production data
- ✅ **Environment Separation**: Complete isolation
- ✅ **Future TaskMaster**: Safe scaling with isolated test environment
- ✅ **Cleanup Verification**: Automated verification and guaranteed cleanup

**Your production Listless database will remain completely clean of test data.**
